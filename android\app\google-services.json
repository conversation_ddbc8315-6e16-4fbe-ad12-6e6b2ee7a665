{"project_info": {"project_number": "843456771665", "project_id": "foodking-inilabs", "storage_bucket": "foodking-inilabs.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:843456771665:android:3540599d5900f4c3582a70", "android_client_info": {"package_name": "com.inilabs.foodking"}}, "oauth_client": [{"client_id": "843456771665-pi46ra110f1t774nipad865tclh79e5g.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBHkY58zN2KjNKUN6fomyXT_c-2c0jXROg"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "843456771665-pi46ra110f1t774nipad865tclh79e5g.apps.googleusercontent.com", "client_type": 3}, {"client_id": "843456771665-dnmqf8030vqkqupag2u0daidgg1db9ut.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.inilabs.foodking.ios"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:843456771665:android:7dc7c9d2fe93e241582a70", "android_client_info": {"package_name": "com.inilabs.foodking.delivery"}}, "oauth_client": [{"client_id": "843456771665-pi46ra110f1t774nipad865tclh79e5g.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBHkY58zN2KjNKUN6fomyXT_c-2c0jXROg"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "843456771665-pi46ra110f1t774nipad865tclh79e5g.apps.googleusercontent.com", "client_type": 3}, {"client_id": "843456771665-dnmqf8030vqkqupag2u0daidgg1db9ut.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.inilabs.foodking.ios"}}]}}}], "configuration_version": "1"}