name: foodking_delivery_boy
version: 1.0.3+13
publish_to: none
description: A new Flutter project.
environment:
  sdk: ">=3.8.1 <4.0.0"

dependencies:
  cupertino_icons: ^1.0.2
  get: ^4.6.5
  http: ^1.2.1
  animated_bottom_navigation_bar: ^1.1.0
  flutter_screenutil: ^5.9.0
  image_picker: ^1.1.0
  get_storage: ^2.0.3
  flutter_svg: ^2.0.10+1
  lottie: ^3.1.0
  modal_bottom_sheet: ^3.0.0
  expansion_tile_card: ^3.0.0
  bottom_bar: ^2.0.2
  rflutter_alert: ^2.0.4
  cached_network_image: ^3.3.1
  shimmer: ^3.0.0
  connectivity_plus: ^6.1.1
  firebase_messaging: ^15.1.0
  firebase_core: ^3.4.0
  flutter_local_notifications: ^18.0.1
  flutter_launcher_icons: ^0.14.1
  google_maps_flutter: ^2.2.5
  location: ^7.0.0
  geolocator: ^13.0.2
  flutter_polyline_points: ^2.0.0
  url_launcher: ^6.1.10
  map_launcher: ^3.3.0
  webview_flutter: ^4.0.7
  flutter_html: any
  win32: ^5.5.3
  google_fonts: any

  flutter:
    sdk: flutter

dev_dependencies:
  flutter_lints: ^5.0.0
  flutter_test:
    sdk: flutter

flutter_icons:
  image_path: "assets/images/app_logo.png"
  android: "ic_launcher"
  ios: true

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/

  fonts:
    - family: Rubik
      fonts:
        - asset: assets/fonts/Rubik-Regular.ttf
          weight: 400
        - asset: assets/fonts/Rubik-Medium.ttf
          weight: 500
        - asset: assets/fonts/Rubik-Bold.ttf
          weight: 700
        - asset: assets/fonts/Rubik-Black.ttf
          weight: 900
