import 'package:flutter/cupertino.dart';

class AppColor {
  static const Color primaryColor = Color(0xffFF006B);
  static const Color primaryBackgroundColor = Color(0xffFFFBFB);
  static const Color loginButtonColor = Color(0xffFF006B);
  static const Color fontColor = Color(0xff1F1F39);
  static const Color dividerColor = Color(0xffEFF0F6);
  static const Color textSignupColor = Color(0xff6E7191);
  static const Color addNewBtnColor = Color(0xffFFEDF4);
  static const Color myAddrBgColor = Color(0xffF7F7FC);
  static const Color homeIconColor = Color(0xff008BBA);
  static const Color activeTxtColor = Color(0xff008BBA);
  static const Color activeTxtBorderColor = Color(0xff6FF8A6);
  static const Color deleveryColor = Color(0xffBDEFFF);
  static const Color deleteBtnColor = Color(0xffE93C3C);
  static const Color searchBarbg = Color(0xffF7F7FC);
  static const Color viewAllbg = Color(0xffFFEDF4);
  static const Color itembg = Color(0xffEFF0F6);
  static const Color delivaryActive = Color(0xff008BBA);
  static const Color delivaryInactive = Color(0xffBDEFFF);
  static const Color green = Color(0xff1AB759);
  static const Color yellow = Color(0xffFFDB1F);
  static const Color gray = Color(0xff6E7191);
  static const Color bgColor = Color(0xffF7F7FC);
  static const Color darkGray = Color(0xff1F1F39);

  //new added
  static const Color cardGreen = Color(0xffD2FFF7);
  static const Color cardsky = Color(0xffDBF6FF);
  static const Color primaryColorWithOpacity = Color(0xffFFEDF4);
  static const Color darkBlue = Color(0xff008BBA);
  static const Color warning = Color(0xffF6A609);
  static const Color success = Color(0xff2AC769);
  static const Color error = Color(0xffFB4E4E);
  static const Color pending = Color(0xffFFEEC6);
  static const Color pendingText = Color(0xffF6A609);
  static const Color preparing = Color(0xffE1FFED);
  static const Color preparingText = Color(0xff2AC769);
  static const Color ontheway = Color(0xffBDEFFF);
  static const Color delivered = Color(0xffFFD7E7);
  static const Color canceled = Color(0xffFFDADA);
  static const Color shadow = Color(0xff7E858E);
}

class Images {
  static String get logo => 'assets/images/logo.png';
  static String get appLogo => 'assets/images/app_logo.png';
  static String get slpashLogo => 'assets/images/splash_logo.png';
  static String get iconOrderComplete => 'assets/icons/order_complete.svg';
  static String get iconReturnDelivery => 'assets/icons/return_delivery.svg';
  static String get iconLocation => 'assets/icons/location.svg';
  static String get iconHome => 'assets/images/home.png';
  static String get iconHistory => 'assets/images/history.png';
  static String get iconProfile => 'assets/images/profile.png';
  static String get iconEmptyOrder => 'assets/images/order_empty.png';
  static String get iconTickedNo => 'assets/icons/ticked-no-square.svg';
  static String get iconTickedYes => 'assets/icons/ticked-yes-square.svg';
  static String get iconArrowRight => 'assets/icons/arrow-right.svg';
  static String get back => 'assets/icons/back.svg';
  static String get imageAvatar => 'assets/images/avatar.png';
  static String get iconEdit => 'assets/images/edit.png';
  static String get iconEditProfile => 'assets/icons/icon_edit.svg';
  static String get iconChangePass => 'assets/icons/key.svg';
  static String get iconChangeLang => 'assets/icons/lang.svg';
  static String get iconTermsCondition => 'assets/icons/doc.svg';
  static String get iconPrivacy => 'assets/icons/privacy.svg';
  static String get iconLogout => 'assets/icons/logout.svg';
  static String get iconRoundedTicked => 'assets/icons/tick-circle.svg';
  static String get flagEn => 'assets/images/en.png';
  static String get flagDe => 'assets/images/de.png';
  static String get flagBd => 'assets/images/bd.png';
  static String get flagAr => 'assets/images/ar.png';
  static String get flagFr => 'assets/images/fr.png';
  static String get iconCall => 'assets/icons/call.svg';
  static String get iconConfirmed => 'assets/images/confirm.png';
  static String get iconPaymentConfirmed => 'assets/images/collect_payment.png';
  static String get dotCircle => 'assets/images/dot_circle.png';
  static String get lostConnection => 'assets/images/lost_connection.png';
  static String get iconBranch => 'assets/icons/branch.svg';
  static String get termsCondition => 'assets/icons/terms_condition.svg';
  static String get locationService => 'assets/icons/location_service.svg';
}
