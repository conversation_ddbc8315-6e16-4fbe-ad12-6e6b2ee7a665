allprojects {
    repositories {
        google()
        mavenCentral()
    }

    tasks.withType(JavaCompile) {
        options.compilerArgs << '-Xlint:-options' 
    }
}

rootProject.buildDir = '../build'
subprojects {
    afterEvaluate { project ->
        if (project.plugins.hasPlugin("com.android.application") ||
                project.plugins.hasPlugin("com.android.library")) {
            project.android {
                compileSdkVersion 34                
            }
        }
    }
}
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
