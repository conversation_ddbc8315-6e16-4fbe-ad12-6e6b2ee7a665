PODS:
  - connectivity (0.0.1):
    - Flutter
    - Reachability
  - Firebase/CoreOnly (10.7.0):
    - FirebaseCore (= 10.7.0)
  - Firebase/Messaging (10.7.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.7.0)
  - firebase_core (2.9.0):
    - Firebase/CoreOnly (= 10.7.0)
    - Flutter
  - firebase_messaging (14.4.0):
    - Firebase/Messaging (= 10.7.0)
    - firebase_core
    - Flutter
  - FirebaseCore (10.7.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Logger (~> 7.8)
  - FirebaseCoreInternal (10.9.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseInstallations (10.9.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.7.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - Flutter (1.0.0)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - FMDB (2.7.5):
    - FMDB/standard (= 2.7.5)
  - FMDB/standard (2.7.5)
  - geolocator_apple (1.2.0):
    - Flutter
  - google_maps_flutter_ios (0.0.1):
    - Flutter
    - GoogleMaps
  - GoogleDataTransport (9.2.3):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleMaps (7.4.0):
    - GoogleMaps/Maps (= 7.4.0)
  - GoogleMaps/Base (7.4.0)
  - GoogleMaps/Maps (7.4.0):
    - GoogleMaps/Base
  - GoogleUtilities/AppDelegateSwizzler (7.11.1):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
  - GoogleUtilities/Environment (7.11.1):
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.11.1):
    - GoogleUtilities/Environment
  - GoogleUtilities/Network (7.11.1):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.11.1)"
  - GoogleUtilities/Reachability (7.11.1):
    - GoogleUtilities/Logger
  - GoogleUtilities/UserDefaults (7.11.1):
    - GoogleUtilities/Logger
  - image_picker_ios (0.0.1):
    - Flutter
  - location (0.0.1):
    - Flutter
  - map_launcher (0.0.1):
    - Flutter
  - nanopb (2.30909.0):
    - nanopb/decode (= 2.30909.0)
    - nanopb/encode (= 2.30909.0)
  - nanopb/decode (2.30909.0)
  - nanopb/encode (2.30909.0)
  - path_provider_ios (0.0.1):
    - Flutter
  - PromisesObjC (2.2.0)
  - Reachability (3.2)
  - sqflite (0.0.2):
    - Flutter
    - FMDB (>= 2.7.5)
  - url_launcher_ios (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter

DEPENDENCIES:
  - connectivity (from `.symlinks/plugins/connectivity/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/ios`)
  - google_maps_flutter_ios (from `.symlinks/plugins/google_maps_flutter_ios/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - location (from `.symlinks/plugins/location/ios`)
  - map_launcher (from `.symlinks/plugins/map_launcher/ios`)
  - path_provider_ios (from `.symlinks/plugins/path_provider_ios/ios`)
  - sqflite (from `.symlinks/plugins/sqflite/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - FMDB
    - GoogleDataTransport
    - GoogleMaps
    - GoogleUtilities
    - nanopb
    - PromisesObjC
    - Reachability

EXTERNAL SOURCES:
  connectivity:
    :path: ".symlinks/plugins/connectivity/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/ios"
  google_maps_flutter_ios:
    :path: ".symlinks/plugins/google_maps_flutter_ios/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  location:
    :path: ".symlinks/plugins/location/ios"
  map_launcher:
    :path: ".symlinks/plugins/map_launcher/ios"
  path_provider_ios:
    :path: ".symlinks/plugins/path_provider_ios/ios"
  sqflite:
    :path: ".symlinks/plugins/sqflite/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"

SPEC CHECKSUMS:
  connectivity: c4130b2985d4ef6fd26f9702e886bd5260681467
  Firebase: 0219acf760880eeec8ce479895bd7767466d9f81
  firebase_core: d85432877e814811e040e7659f9c82faeab66e04
  firebase_messaging: 45c0514ca78426630338a42fb7b55af962c7ccd2
  FirebaseCore: e317665b9d744727a97e623edbbed009320afdd7
  FirebaseCoreInternal: d2b4acb827908e72eca47a9fd896767c3053921e
  FirebaseInstallations: c58489c9caacdbf27d1da60891a87318e20218e0
  FirebaseMessaging: ac9062bcc35ed56e15a0241d8fd317022499baf8
  Flutter: f04841e97a9d0b0a8025694d0796dd46242b2854
  flutter_local_notifications: 0c0b1ae97e741e1521e4c1629a459d04b9aec743
  FMDB: 2ce00b547f966261cd18927a3ddb07cb6f3db82a
  geolocator_apple: cc556e6844d508c95df1e87e3ea6fa4e58c50401
  google_maps_flutter_ios: 3e0b99383a8003b8169d06e7e324170bd0424105
  GoogleDataTransport: f0308f5905a745f94fb91fea9c6cbaf3831cb1bd
  GoogleMaps: 032f676450ba0779bd8ce16840690915f84e57ac
  GoogleUtilities: 9aa0ad5a7bc171f8bae016300bfcfa3fb8425749
  image_picker_ios: b786a5dcf033a8336a657191401bfdf12017dabb
  location: 3a2eed4dd2fab25e7b7baf2a9efefe82b512d740
  map_launcher: e325db1261d029ff33e08e03baccffe09593ffea
  nanopb: b552cce312b6c8484180ef47159bc0f65a1f0431
  path_provider_ios: 14f3d2fd28c4fdb42f44e0f751d12861c43cee02
  PromisesObjC: 09985d6d70fbe7878040aa746d78236e6946d2ef
  Reachability: 33e18b67625424e47b6cde6d202dce689ad7af96
  sqflite: 6d358c025f5b867b29ed92fc697fd34924e11904
  url_launcher_ios: 08a3dfac5fb39e8759aeb0abbd5d9480f30fc8b4
  webview_flutter_wkwebview: 2e2d318f21a5e036e2c3f26171342e95908bd60a

PODFILE CHECKSUM: b34a2cba678af73a4b07f9184ef5c95e6772a626

COCOAPODS: 1.12.1
