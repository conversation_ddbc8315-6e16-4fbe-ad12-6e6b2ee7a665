{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\update plan for april 25\\foodking-delivery-app\\android\\app\\.cxx\\Debug\\4n456k4m\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\update plan for april 25\\foodking-delivery-app\\android\\app\\.cxx\\Debug\\4n456k4m\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}